# Design Document

## Overview

The Interactive Plan Approval System redesigns the user interaction flow for multi-step plan approval in BlendPro. Instead of requiring text-based responses, the system will present plans as interactive chat messages with embedded approval buttons. This design maintains the existing architectural patterns while introducing new message types and operator behaviors.

## Architecture

### Current Flow vs New Flow

**Current Flow:**
1. AI generates plan → Display plan text → Wait for "yes"/"no" input → Execute or reject

**New Flow:**
1. AI generates plan → Display interactive plan message with buttons → Button click triggers direct execution

### Core Components

1. **Enhanced Message System**: Extended chat history with interactive message support
2. **Plan Storage**: Temporary plan data storage within message objects
3. **Interactive UI Rendering**: Modified panel draw logic for button rendering
4. **Direct Operator Execution**: Updated operators that bypass text input simulation

## Components and Interfaces

### 1. Message System Extensions

```python
# Extended message properties
class ChatMessage:
    type: str  # 'user', 'assistant', 'assistant_plan'
    content: str
    is_interactive: bool = False  # NEW
    plan_data: str = ""  # JSON serialized plan steps
```

### 2. Plan Approval Operators

```python
class BLENDPRO_OT_ApprovePlan(bpy.types.Operator):
    bl_idname = "blendpro.approve_plan"
    plan_steps_json: bpy.props.StringProperty()
    
    def execute(self, context):
        # Deserialize plan steps
        # Execute background plan execution
        # Update chat history with approval message
        
class BLENDPRO_OT_RejectPlan(bpy.types.Operator):
    bl_idname = "blendpro.reject_plan"
    original_prompt: bpy.props.StringProperty()
    
    def execute(self, context):
        # Update chat history with rejection message
        # Attempt single-step execution of original prompt
```

### 3. Enhanced Panel Drawing

```python
def draw_interactive_message(self, layout, message):
    box = layout.box()
    box.label(text="AI Generated Plan:")
    
    # Display plan steps
    for line in message.content.split('\n'):
        box.label(text=line)
    
    # Action buttons
    row = box.row(align=True)
    approve_op = row.operator("blendpro.approve_plan", 
                             text="✓ Execute Plan", 
                             icon="CHECKMARK")
    approve_op.plan_steps_json = message.plan_data
    
    reject_op = row.operator("blendpro.reject_plan", 
                            text="✗ Try Single Step", 
                            icon="X")
```

### 4. Modified Execution Flow

```python
# In generate_enhanced_blender_code()
if is_complex_task(prompt):
    steps = plan_complex_task(prompt, context, api_key, base_url, model)
    if steps and len(steps) > 1:
        return {
            "error": None,
            "code": None,
            "is_plan_preview": True,  # NEW FLAG
            "steps": steps,
            "plan_preview": format_step_plan_for_user(steps),
            "original_prompt": prompt  # For rejection fallback
        }
```

## Data Models

### Plan Step Structure
```python
{
    "step_number": int,
    "description": str,
    "code": str,
    "dependencies": List[int],  # Previous step numbers required
    "estimated_duration": float  # Optional
}
```

### Interactive Message Structure
```python
{
    "type": "assistant",
    "content": "Plan preview text",
    "is_interactive": True,
    "plan_data": "JSON serialized steps array",
    "metadata": {
        "original_prompt": str,
        "step_count": int,
        "created_timestamp": float
    }
}
```

## Error Handling

### Plan Execution Errors
- **Step Failure**: If a plan step fails, execution stops and error is reported in chat
- **Serialization Errors**: Plan data corruption handled with graceful fallback to single-step execution
- **UI State Errors**: Interactive buttons disabled if plan data is invalid

### Fallback Mechanisms
- **Plan Rejection**: Automatically attempts single-step execution of original prompt
- **Invalid Plan Data**: Falls back to text-based approval if interactive mode fails
- **Operator Failures**: Error messages displayed in chat with recovery suggestions

## Testing Strategy

### Unit Tests
1. **Message Serialization**: Test plan data JSON serialization/deserialization
2. **Operator Execution**: Test approve/reject operators with mock plan data
3. **UI Rendering**: Test interactive message rendering with various plan sizes

### Integration Tests
1. **End-to-End Flow**: Test complete flow from complex task detection to plan execution
2. **Error Recovery**: Test fallback mechanisms when plan execution fails
3. **UI Interaction**: Test button clicks and state management

### User Acceptance Tests
1. **Plan Preview Clarity**: Verify users can understand plan steps before approval
2. **Button Responsiveness**: Ensure immediate feedback when buttons are clicked
3. **Error Communication**: Verify clear error messages when plans fail

## Implementation Phases

### Phase 1: Core Infrastructure
- Extend message system with interactive properties
- Modify panel drawing for interactive messages
- Update plan generation to return preview data

### Phase 2: Operator Implementation
- Implement approve/reject operators
- Add plan data serialization/storage
- Integrate with existing execution system

### Phase 3: UI Polish & Error Handling
- Add visual feedback for button interactions
- Implement comprehensive error handling
- Add progress indicators for plan execution

### Phase 4: Testing & Optimization
- Comprehensive testing of all flows
- Performance optimization for large plans
- User experience refinements